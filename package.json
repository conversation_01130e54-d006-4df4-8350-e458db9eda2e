{"name": "test-verification-report-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "lucide-react": "^0.525.0", "@google/genai": "^1.9.0", "recharts": "^3.1.0", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "pako": "^2.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}