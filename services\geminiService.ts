import { GoogleGenAI, Chat, GenerateContentResponse, Type } from "@google/genai";
import { ReportData, ChatMessage } from '../types';

const API_KEY_SESSION_STORAGE_KEY = 'gemini-api-key';

// --- API Key Management ---

/**
 * Checks if the API key is set in session storage.
 * @returns {boolean} True if the key is set, false otherwise.
 */
export const isApiKeySet = (): boolean => {
    if (typeof sessionStorage === 'undefined') return false;
    return sessionStorage.getItem(API_KEY_SESSION_STORAGE_KEY) !== null;
};

/**
 * Retrieves the API key from session storage.
 * @returns {string | null} The API key or null if not set.
 */
const getApiKey = (): string | null => {
    if (typeof sessionStorage === 'undefined') return null;
    return sessionStorage.getItem(API_KEY_SESSION_STORAGE_KEY);
};

/**
 * Saves the API key to session storage.
 * @param {string} apiKey The API key to save.
 */
export const setApiKey = (apiKey: string): void => {
    if (typeof sessionStorage === 'undefined') return;
    sessionStorage.setItem(API_KEY_SESSION_STORAGE_KEY, apiKey);
};

/**
 * Gets an initialized GoogleGenAI client.
 * Throws an error if the API key is not set.
 * @returns {GoogleGenAI} An initialized AI client.
 */
const getAiClient = (): GoogleGenAI => {
    const apiKey = getApiKey();
    if (!apiKey) {
        throw new Error("Gemini API key not found in session storage. Please set it before using AI features.");
    }
    return new GoogleGenAI({ apiKey });
};


// --- Service Functions ---

const generateTrendSummary = (reports: ReportData[]): string => {
    if (reports.length <= 1) return '';

    const firstReport = reports[0];
    const lastReport = reports[reports.length - 1];

    const passRateTrend = `The pass rate has trended from ${firstReport.summary.passRate}% to ${lastReport.summary.passRate}%.`;
    const defectsTrend = `The number of defects found per report has been: ${reports.map(r => r.summary.totalDefects).join(', ')}.`;

    return `
First, here is a high-level summary of the trends across the ${reports.length} reports provided:
- **Timeline**: The reports span from a start date of ${firstReport.summary.testPeriod.split(' - ')[0]} to an end date of ${lastReport.summary.testPeriod.split(' - ')[1]}.
- **Pass Rate Trend**: ${passRateTrend}
- **Defects Found Trend**: ${defectsTrend}

Now, let's focus on the details of the LATEST report.
`;
};

export const createChat = (reports: ReportData[], languageName: string): Chat => {
    const ai = getAiClient();
    const latestReport = reports[reports.length - 1];
    const trendSummary = generateTrendSummary(reports);

    const systemInstruction = `You are a world-class senior QA manager and engineering director. You are in a conversation with a team member about a series of test verification reports. Your task is to answer their questions concisely and strategically, based ONLY on the provided data.
${trendSummary}
Here is the summary of the LATEST test report:

**Project:** ${latestReport.subtitle} Release Verification
**Test Period:** ${latestReport.summary.testPeriod}
**Overall Pass Rate:** ${latestReport.summary.passRate}% (${Math.round(latestReport.summary.totalTestCases * (latestReport.summary.passRate / 100)).toLocaleString()} pass / ${latestReport.summary.totalTestCases.toLocaleString()} total cases)
**Total Defects Found:** ${latestReport.summary.totalDefects}
**Defect Breakdown by Priority:**
${latestReport.priorityData.map(p => `- ${p.name}: ${p.value}`).join('\n')}
**Defect Breakdown by Status:**
${latestReport.statusData.map(s => `- ${s.name}: ${s.value}`).join('\n')}
**Key High-Severity Defects & Risks:**
${latestReport.majorDefects.map(d => `- **${d.id}:** ${d.title} - ${d.description}`).join('\n')}

When answering, be professional and helpful. If asked about trends, use the historical context. If asked about specifics, use the latest report data.
**IMPORTANT:** You MUST respond in the following language: ${languageName}.`;

    return ai.chats.create({
        model: 'gemini-2.5-flash',
        config: {
            systemInstruction: systemInstruction,
        },
    });
};

export const parseFileWithAI = async (fileBase64: string, mimeType: string): Promise<ReportData> => {
     const ai = getAiClient();
     const filePart = {
        inlineData: {
            data: fileBase64,
            mimeType: mimeType
        }
    };

    const textPart = {
        text: `You are a data extraction expert. The user has uploaded a file. Analyze its content and extract the data into a valid JSON object that strictly follows this TypeScript interface:
        
        interface ReportData {
            reportTitle: string; // e.g., "Test Verification Report"
            subtitle: string; // e.g., "BiNDec MODEL Sprint 1" or "BiNDec MODEL Q1"
            summary: {
                testPeriod: string; // e.g., "2025/04/28 - 2025/05/04". IMPORTANT: This must be a real date range from the document.
                totalTestCases: number; // e.g., 2757
                passRate: number; // e.g., 91 (just the number)
                totalDefects: number; // e.g., 239
            };
            priorityData: { name: string; value: number; fill: string; }[]; // Extract High, Middle, Low. Use fills: '#ef4444' (high), '#f59e0b' (middle), '#3b82f6' (low)
            statusData: { name: string; value: number; fill: string; }[]; // Extract Open, In Progress, Closed. Use fills: '#ef4444' (open), '#f59e0b' (in progress), '#22c55e' (closed)
            majorDefects: { id: string; title: string; description: string; severity: 'High'; }[]; // Extract at least 4 high-priority defects.
            testEnvironment: {
                os: string[]; // e.g., ["macOS", "Windows 11", "iOS", "Android"]
                browsers: string[]; // e.g., ["Chrome", "Edge", "Safari"]
                store: string; // e.g., "dev-bindec-model-theme"
            };
        }

        Analyze the document and return ONLY the JSON object. Do not include any introductory text, explanations, or markdown formatting like \`\`\`json. Your entire response must be the raw JSON text. If you cannot find specific data, make a reasonable estimate based on the context. Ensure the JSON is well-formed.`
    };

    try {
        const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: { parts: [filePart, textPart] },
        });

        const jsonText = response.text.trim();
        const parsedData = JSON.parse(jsonText);
        return parsedData as ReportData;

    } catch (error) {
        console.error("Error parsing file with AI:", error);
        if (error instanceof Error) {
            return Promise.reject(new Error(`Failed to parse file with Gemini API: ${error.message}. Ensure the document contains recognizable report data.`));
        }
        return Promise.reject(new Error("An unknown error occurred while parsing the file with the AI."));
    }
};