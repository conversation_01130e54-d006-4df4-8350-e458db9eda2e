import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DefectList, ImportScreen, Chatbot, LineChart, StackedBarChart, JiraModal, ScrollToTopButton, ApiKeyModal } from './components';
import { getSampleReportData, getSampleXlsxData } from './constants';
import { ReportData, Defect, ChatMessage, JiraConfig } from './types';
import { downloadJson, downloadBlob } from './utils/fileUtils';
import { createChat, isApiKeySet } from './services/geminiService';
import { TestTube, CheckCircle, AlertTriangle, ListChecks, Server, Globe, Cpu, XCircle } from 'lucide-react';
import { useTranslations } from './hooks/useTranslations';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import pako from 'pako';

const App: React.FC = () => {
    const [isKeySet, setIsKeySet] = useState(isApiKeySet());
    const [originalReports, setOriginalReports] = useState<ReportData[] | null>(null);
    const [displayedReports, setDisplayedReports] = useState<ReportData[] | null>(null);
    const [chat, setChat] = useState<any>(null); // Gemini Chat instance
    const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
    const [isBotLoading, setIsBotLoading] = useState<boolean>(false);
    const [priorityFilter, setPriorityFilter] = useState<string | null>(null);
    const [isJiraModalOpen, setIsJiraModalOpen] = useState(false);
    const [selectedDefect, setSelectedDefect] = useState<Defect | null>(null);

    const { t, language } = useTranslations();
    
    // Check for shared data in URL on initial load
    useEffect(() => {
        try {
            const hash = window.location.hash.substring(1);
            if (hash.startsWith('data=')) {
                const compressedDataString = atob(hash.substring(5));
                const uint8ArrayData = Uint8Array.from(compressedDataString, c => c.charCodeAt(0));
                const inflatedData = pako.inflate(uint8ArrayData, { to: 'string' });
                const reports = JSON.parse(inflatedData);
                if (Array.isArray(reports) && reports.length > 0) {
                   handleImport(reports, false);
                }
            }
        } catch (error) {
            console.error("Failed to load shared data from URL:", error);
            // Clear hash to prevent repeated errors
            window.location.hash = '';
        }
    }, []);

    const handleImport = (data: ReportData[], clearShareUrl: boolean = true) => {
        const sortedData = data.sort((a, b) => {
            const dateA = new Date(a.summary.testPeriod.split(' - ')[1]);
            const dateB = new Date(b.summary.testPeriod.split(' - ')[1]);
            return dateA.getTime() - dateB.getTime();
        });
        setOriginalReports(sortedData);
        setChat(null);
        setChatHistory([]);
        setPriorityFilter(null);

        if (clearShareUrl) {
            window.location.hash = '';
        }
    };

    const handleReset = () => {
        setOriginalReports(null);
        setDisplayedReports(null);
        window.location.hash = '';
    };

    const handleDownloadSample = useCallback((format: 'json' | 'xlsx') => {
        if (format === 'json') {
            downloadJson(getSampleReportData(t), 'sample-report.json');
        } else {
            const blob = getSampleXlsxData(t);
            downloadBlob(blob, 'sample-report.xlsx');
        }
    }, [t]);

    const handleExportPdf = () => {
        const dashboardElement = document.getElementById('dashboard-content');
        if (dashboardElement) {
            const originalBg = dashboardElement.style.backgroundColor;
            const theme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
            dashboardElement.style.backgroundColor = theme === 'dark' ? '#0f172a' : '#ffffff';

            html2canvas(dashboardElement, { 
                useCORS: true, 
                scale: 2,
                backgroundColor: theme === 'dark' ? '#0f172a' : '#ffffff',
            }).then(canvas => {
                dashboardElement.style.backgroundColor = originalBg;
                const latestReport = displayedReports?.[displayedReports.length - 1];
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'px',
                    format: [canvas.width, canvas.height]
                });
                pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height);
                pdf.save(`report-${latestReport?.subtitle || 'dashboard'}.pdf`);
            });
        }
    };
    
    const handleShare = () => {
        if (originalReports) {
            try {
                const jsonString = JSON.stringify(originalReports);
                const compressed = pako.deflate(jsonString);
                const base64 = btoa(String.fromCharCode.apply(null, Array.from(compressed)));
                const url = `${window.location.origin}${window.location.pathname}#data=${base64}`;
                navigator.clipboard.writeText(url).then(() => {
                    alert(t('share.success'));
                }, () => {
                    alert(t('share.fail'));
                });
            } catch (e) {
                alert(t('share.error'));
                console.error("Sharing error:", e);
            }
        }
    };

    useEffect(() => {
        if (!originalReports) {
            setDisplayedReports(null);
            return;
        }

        const translatedDefectsArray = t('sampleReport.majorDefects', { returnObjects: true }) as Defect[];
        const translationsMap = new Map<string, { title: string; description: string }>();

        if (Array.isArray(translatedDefectsArray)) {
            translatedDefectsArray.forEach(d => {
                translationsMap.set(d.id, { title: d.title, description: d.description });
            });
        }

        const processedReports: ReportData[] = originalReports.map(report => {
            const newReport = JSON.parse(JSON.stringify(report));
            newReport.majorDefects = newReport.majorDefects.map((defect: Defect) => {
                const translation = translationsMap.get(defect.id);
                // Ensure annotations array exists
                if (!defect.annotations) {
                  defect.annotations = [];
                }
                return translation ? { ...defect, title: translation.title, description: translation.description } : defect;
            });
            return newReport;
        });

        setDisplayedReports(processedReports);

        const languageName = t('language');
        const chatInstance = createChat(processedReports, languageName);
        setChat(chatInstance);
        setChatHistory([]);

    }, [originalReports, t]);
    
    const handlePriorityClick = (data: any) => {
        if (data && data.name) {
            setPriorityFilter(prevFilter => prevFilter === data.name ? null : data.name);
        }
    };
    
    const clearFilter = () => setPriorityFilter(null);

    const handleSendMessage = async (message: string) => {
        if (!chat) return;

        const userMessage: ChatMessage = { role: 'user', parts: [{ text: message }] };
        setChatHistory(prev => [...prev, userMessage]);
        setIsBotLoading(true);

        try {
            const stream = await chat.sendMessageStream({ message });
            let botResponse = '';
            setChatHistory(prev => [...prev, { role: 'model', parts: [{ text: '' }] }]);
            
            for await (const chunk of stream) {
                botResponse += chunk.text;
                setChatHistory(prev => {
                    const newHistory = [...prev];
                    newHistory[newHistory.length - 1] = { role: 'model', parts: [{ text: botResponse }] };
                    return newHistory;
                });
            }
        } catch (error) {
            console.error("Chat error:", error);
            const errorMessage: ChatMessage = { role: 'model', parts: [{ text: "Sorry, I encountered an error." }] };
            setChatHistory(prev => [...prev, errorMessage]);
        } finally {
            setIsBotLoading(false);
        }
    };

    const handleJiraClick = (defect: Defect) => {
        setSelectedDefect(defect);
        setIsJiraModalOpen(true);
    };

    const handleAddAnnotation = (defectId: string, note: string) => {
        if (!displayedReports) return;

        const updatedReports = displayedReports.map(report => {
            return {
                ...report,
                majorDefects: report.majorDefects.map(defect => {
                    if (defect.id === defectId) {
                        return {
                            ...defect,
                            annotations: [...(defect.annotations || []), note],
                        };
                    }
                    return defect;
                })
            };
        });
        setDisplayedReports(updatedReports);
        
        // Also update the original reports so they are included in shares
        const updatedOriginalReports = originalReports!.map(report => {
           return {
                ...report,
                majorDefects: report.majorDefects.map(defect => {
                    if (defect.id === defectId) {
                        return {
                            ...defect,
                            annotations: [...(defect.annotations || []), note],
                        };
                    }
                    return defect;
                })
            };
        });
        setOriginalReports(updatedOriginalReports);
    };

    if (!isKeySet) {
        return <ApiKeyModal onSuccess={() => setIsKeySet(true)} />;
    }

    if (!displayedReports) {
        return <ImportScreen onImport={handleImport} onDownloadSample={handleDownloadSample} />;
    }
    
    const latestReport = displayedReports[displayedReports.length - 1];
    const passCases = Math.round(latestReport.summary.totalTestCases * (latestReport.summary.passRate / 100));
    
    const filteredDefects = priorityFilter 
        ? latestReport.majorDefects.filter(d => d.severity === 'High') // Assuming all major defects are High priority, but filter just in case
        : latestReport.majorDefects;

    const passRateTrendData = displayedReports.map(report => ({
        name: report.subtitle,
        [t('charts.passRate')]: report.summary.passRate,
    }));

    const defectStatusTrendData = displayedReports.map(report => {
        const statusMap = report.statusData.reduce((acc, curr) => {
            acc[curr.name] = curr.value;
            return acc;
        }, {} as Record<string, number>);
        
        return {
            name: report.subtitle,
            [t('sampleReport.defectStatus.open')]: statusMap[t('sampleReport.defectStatus.open')] || 0,
            [t('sampleReport.defectStatus.inProgress')]: statusMap[t('sampleReport.defectStatus.inProgress')] || 0,
            [t('sampleReport.defectStatus.closed')]: statusMap[t('sampleReport.defectStatus.closed')] || 0,
        };
    });


    return (
        <div className="min-h-screen bg-light-background dark:bg-dark-background text-light-text-primary dark:text-dark-text-primary font-sans p-4 sm:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
                <Header title={latestReport.reportTitle} subtitle={latestReport.subtitle} onReset={handleReset} onExportPdf={handleExportPdf} onShare={handleShare}/>
                
                <main id="dashboard-content" className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

                    {displayedReports.length > 1 && (
                        <div className="md:col-span-2 lg:col-span-4 bg-light-surface dark:bg-dark-surface p-6 rounded-2xl shadow-lg">
                            <h3 className="text-xl font-semibold text-light-text-primary dark:text-dark-text-primary mb-4">{t('dashboard.trends.title')}</h3>
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <div>
                                    <h4 className="font-semibold text-center mb-2">{t('dashboard.trends.passRateTitle')}</h4>
                                    <LineChart data={passRateTrendData} />
                                </div>
                                <div>
                                    <h4 className="font-semibold text-center mb-2">{t('dashboard.trends.defectStatusTitle')}</h4>
                                    <StackedBarChart data={defectStatusTrendData} />
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="md:col-span-2 lg:col-span-4 mt-4">
                        <h2 className="text-2xl font-bold text-light-text-primary dark:text-dark-text-primary mb-2">{t('dashboard.latestReportTitle')}</h2>
                        <hr className="border-slate-300 dark:border-slate-600"/>
                    </div>

                    <SummaryCard title={t('dashboard.summaryCardTitles.testPeriod')} value={latestReport.summary.testPeriod} icon={<TestTube className="w-8 h-8 text-sky-500" />} />
                    <SummaryCard title={t('dashboard.summaryCardTitles.totalTestCases')} value={latestReport.summary.totalTestCases.toLocaleString()} icon={<ListChecks className="w-8 h-8 text-blue-500" />} />
                    <SummaryCard title={t('dashboard.summaryCardTitles.passRate')} value={`${latestReport.summary.passRate}%`} subtitle={`${passCases.toLocaleString()} ${t('dashboard.summaryCardTitles.passed')}`} icon={<CheckCircle className="w-8 h-8 text-green-500" />} />
                    <SummaryCard title={t('dashboard.summaryCardTitles.totalDefects')} value={latestReport.summary.totalDefects.toLocaleString()} subtitle={`${latestReport.summary.totalDefects} ${t('dashboard.summaryCardTitles.itemsFound')}`} icon={<AlertTriangle className="w-8 h-8 text-red-500" />} />

                    <div className="md:col-span-2 lg:col-span-2 bg-light-surface dark:bg-dark-surface p-6 rounded-2xl shadow-lg">
                        <h3 className="text-xl font-semibold text-light-text-primary dark:text-dark-text-primary mb-4">{t('dashboard.defectPriorityTitle')}</h3>
                        <DonutChart data={latestReport.priorityData} onSliceClick={handlePriorityClick} />
                    </div>
                    <div className="md:col-span-2 lg:col-span-2 bg-light-surface dark:bg-dark-surface p-6 rounded-2xl shadow-lg">
                        <h3 className="text-xl font-semibold text-light-text-primary dark:text-dark-text-primary mb-4">{t('dashboard.defectStatusTitle')}</h3>
                        <BarChart data={latestReport.statusData} barName={t('charts.defects')} />
                    </div>

                    <div className="md:col-span-2 lg:col-span-4 bg-light-surface dark:bg-dark-surface p-6 rounded-2xl shadow-lg">
                        <div className="flex flex-col sm:flex-row justify-between sm:items-center mb-4">
                            <h3 className="text-xl font-semibold text-light-text-primary dark:text-dark-text-primary">{t('dashboard.majorDefectsTitle')}</h3>
                            {priorityFilter && (
                                <button onClick={clearFilter} className="flex items-center gap-2 text-sm text-sky-600 dark:text-sky-400 hover:underline mt-2 sm:mt-0">
                                    <XCircle size={16} />
                                    {t('dashboard.clearFilter')} {priorityFilter}
                                </button>
                            )}
                        </div>
                        <DefectList defects={filteredDefects} onJiraClick={handleJiraClick} onAddAnnotation={handleAddAnnotation} />
                    </div>

                    <div className="md:col-span-2 lg:col-span-4 bg-light-surface dark:bg-dark-surface p-6 rounded-2xl shadow-lg">
                       <h3 className="text-xl font-semibold text-light-text-primary dark:text-dark-text-primary mb-4">{t('dashboard.testEnvironmentTitle')}</h3>
                       <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 text-center">
                            {latestReport.testEnvironment.os.map(os => <div key={os} className="flex items-center justify-center gap-2 p-2 bg-slate-200 dark:bg-slate-700/50 rounded-lg"><Cpu size={16}/><span>{os}</span></div>)}
                            {latestReport.testEnvironment.browsers.map(b => <div key={b} className="flex items-center justify-center gap-2 p-2 bg-slate-200 dark:bg-slate-700/50 rounded-lg"><Globe size={16}/><span>{b}</span></div>)}
                            <div className="flex items-center justify-center gap-2 p-2 bg-slate-200 dark:bg-slate-700/50 rounded-lg"><Server size={16}/><span>{latestReport.testEnvironment.store}</span></div>
                       </div>
                    </div>

                    <div className="md:col-span-2 lg:col-span-4 bg-gradient-to-br from-sky-100 to-white dark:from-sky-900/50 dark:to-dark-surface p-6 rounded-2xl shadow-lg border border-sky-300 dark:border-sky-600/50">
                        <Chatbot 
                            history={chatHistory} 
                            onSendMessage={handleSendMessage}
                            isLoading={isBotLoading}
                        />
                    </div>
                </main>

                <footer className="text-center mt-12 text-light-text-secondary dark:text-dark-text-secondary text-sm">
                    <p>{t('dashboard.footer.generatedFor', { release: latestReport.subtitle, year: new Date().getFullYear().toString() })}</p>
                </footer>
            </div>
             {isJiraModalOpen && selectedDefect && (
                <JiraModal
                    defect={selectedDefect}
                    isOpen={isJiraModalOpen}
                    onClose={() => setIsJiraModalOpen(false)}
                />
            )}
            <ScrollToTopButton />
        </div>
    );
};

export default App;